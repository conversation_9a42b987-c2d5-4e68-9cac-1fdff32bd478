// Тест для ArchiveService
// Этот файл демонстрирует, как можно тестировать ArchiveService

import org.kefir.services.ArchiveService
import org.kefir.services.PackageInfoService

// Mock script object для тестирования
class MockScript {
    def fileExists(String path) {
        if (path == 'package.json') return true
        return false
    }
    
    def readJSON(Map params) {
        return [
            name: 'com.kefir.addon-manager',
            version: '1.1.1'
        ]
    }
    
    def echo(String message) {
        println "ECHO: ${message}"
    }
    
    def sh(String command) {
        println "SHELL: ${command}"
        return ""
    }
    
    def sh(Map params) {
        println "SHELL: ${params.script}"
        if (params.returnStdout) {
            return "1.2M"  // Mock file size
        }
        return ""
    }
}

// Тестовый сценарий
def testArchiveService() {
    def mockScript = new MockScript()
    def archiveService = new ArchiveService(mockScript)
    
    println "=== Тест ArchiveService ==="
    
    // Тест 1: Базовое создание архива
    println "\n1. Тест базового создания архива:"
    def result1 = archiveService.createZipArchive([:])
    println "Результат: ${result1}"
    
    // Тест 2: Создание архива с кастомными исключениями
    println "\n2. Тест с кастомными исключениями:"
    def result2 = archiveService.createZipArchive([
        excludePatterns: 'node_modules/**,test/**,*.log'
    ])
    println "Результат: ${result2}"
    
    // Тест 3: Создание архива с выходной директорией
    println "\n3. Тест с выходной директорией:"
    def result3 = archiveService.createZipArchiveToDirectory([
        outputDirectory: 'artifacts',
        excludePatterns: 'node_modules/**'
    ])
    println "Результат: ${result3}"
    
    println "\n=== Тесты завершены ==="
}

// Запуск тестов
testArchiveService()

/*
 * Ожидаемый вывод:
 * 
 * === Тест ArchiveService ===
 * 
 * 1. Тест базового создания архива:
 * ECHO: Создание архива: addon-manager-1.1.1.zip
 * ECHO: Папка в архиве: addon-manager/
 * SHELL: mkdir -p 'temp_archive_*/addon-manager'
 * SHELL: rsync -av --exclude='node_modules/**' ... ./temp_archive_*/addon-manager/
 * SHELL: cd 'temp_archive_*' && zip -r '../addon-manager-1.1.1.zip' 'addon-manager'
 * SHELL: rm -rf 'temp_archive_*'
 * SHELL: ls -lh 'addon-manager-1.1.1.zip' | awk '{print $5}'
 * Результат: [success:true, log:Архив addon-manager-1.1.1.zip успешно создан (размер: 1.2M), ...]
 * 
 * 2. Тест с кастомными исключениями:
 * ...
 * 
 * 3. Тест с выходной директорией:
 * SHELL: mkdir -p 'artifacts'
 * SHELL: mv 'addon-manager-1.1.1.zip' 'artifacts/'
 * ...
 * 
 * === Тесты завершены ===
 */
