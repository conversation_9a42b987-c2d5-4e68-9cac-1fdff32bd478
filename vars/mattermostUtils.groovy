import org.kefir.services.MattermostService
import org.kefir.services.PackageInfoService
import org.kefir.configs.MattermostConstants

/**
 * Utility functions for Mattermost notifications
 */

/**
 * Sends a notification about successful package publication
 * @param config Map containing notification configuration
 * @return Boolean indicating success
 */
Boolean packagePublished(Map config) {
    def mattermostService = new MattermostService(this)
    return mattermostService.sendPackagePublishedNotification(config)
}

/**
 * Sends a custom message to Mattermost
 * @param config Map containing message configuration
 * @return Boolean indicating success
 */
Boolean sendCustomMessage(Map config) {
    def mattermostService = new MattermostService(this)
    return mattermostService.sendCustomMessage(config)
}

/**
 * Sends a short status message to Mattermost
 * @param config Map containing status configuration
 * @return Boolean indicating success
 */
Boolean sendShortStatus(Map config) {
    def mattermostService = new MattermostService(this)
    return mattermostService.sendShortStatus(config)
}

/**
 * Sends a build failure notification
 * @param config Map containing failure notification configuration
 * @return Boolean indicating success
 */
Boolean buildFailed(Map config) {
    def mattermostService = new MattermostService(this)
    def packageInfoService = new PackageInfoService(this)

    def packageInfo = packageInfoService.getPackageInfo()
    String message

    if (packageInfo.name && packageInfo.version) {
        message = String.format(MattermostConstants.PACKAGE_FAILED_TEMPLATE, packageInfo.name, packageInfo.version) + "\n\n"
    } else {
        message = "${MattermostConstants.BUILD_FAILED_TEMPLATE}\n\n"
    }

    message += "${MattermostConstants.BUILD_INFO_HEADER}\n"
    message += "• Job: [${env.JOB_NAME}](${env.JOB_URL})\n"
    message += "• Build: [#${env.BUILD_NUMBER}](${env.BUILD_URL})\n"

    if (env.BRANCH_NAME) {
        message += "• Branch: ${env.BRANCH_NAME}\n"
    }

    if (config.error?.message) {
        message += "\n**Error:** ${config.error.message}\n"
    }

    return mattermostService.sendCustomMessage([
        channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
        color: MattermostConstants.COLOR_DANGER,
        message: message
    ])
}

/**
 * Sends a build status notification
 * @param config Map containing status configuration
 * @return Boolean indicating success
 */
Boolean sendBuildStatus(Map config) {
    def mattermostService = new MattermostService(this)

    String statusIcon = config.status == 'unstable' ? MattermostConstants.ICON_WARNING : MattermostConstants.ICON_INFO
    String message = String.format(MattermostConstants.BUILD_STATUS_TEMPLATE, statusIcon, config.status?.toUpperCase()) + "\n\n"
    message += "${MattermostConstants.BUILD_INFO_HEADER}\n"
    message += "• Job: [${env.JOB_NAME}](${env.JOB_URL})\n"
    message += "• Build: [#${env.BUILD_NUMBER}](${env.BUILD_URL})\n"

    if (env.BRANCH_NAME) {
        message += "• Branch: ${env.BRANCH_NAME}\n"
    }

    if (config.message) {
        message += "\n${config.message}\n"
    }

    String color = config.status == 'unstable' ? MattermostConstants.COLOR_WARNING : MattermostConstants.COLOR_GOOD

    return mattermostService.sendCustomMessage([
        channel: config.channel ?: MattermostConstants.DEFAULT_CHANNEL,
        color: color,
        message: message
    ])
}
