package org.kefir.services

import org.kefir.configs.ArchiveConstants

class ArchiveService {
    private final def script
    private final PackageInfoService packageInfoService

    ArchiveService(script) {
        this.script = script
        this.packageInfoService = new PackageInfoService(script)
    }

    /**
     * Creates a zip archive with the specified structure:
     * - Archive name: {repo-name}-{version}.zip
     * - Archive contains folder: {repo-name}/
     * - Folder contains all project files (excluding specified patterns)
     * 
     * @param config Map containing archive configuration
     * @return Map with success status and log message
     */
    Map createZipArchive(Map config) {
        try {
            def packageInfo = packageInfoService.getPackageInfo()
            if (!packageInfo.name || !packageInfo.version) {
                return [success: false, log: "Не удалось получить имя пакета или версию из package.json"]
            }

            String repoName = extractRepoName(packageInfo.name)
            String version = packageInfo.version
            
            String archiveName = String.format(ArchiveConstants.ARCHIVE_NAME_PATTERN, repoName, version)
            String folderName = String.format(ArchiveConstants.ARCHIVE_FOLDER_PATTERN, repoName)
            
            script.echo "Создание архива: ${archiveName}"
            script.echo "Папка в архиве: ${folderName}/"
            
            String tempDir = "temp_archive_${System.currentTimeMillis()}"
            String targetDir = "${tempDir}/${folderName}"
            
            script.sh "mkdir -p '${targetDir}'"
            
            String excludePatterns = config.excludePatterns ?: ArchiveConstants.DEFAULT_EXCLUDE_PATTERNS
            copyFilesToArchiveDir(targetDir, excludePatterns)
            
            script.sh "cd '${tempDir}' && zip -r '../${archiveName}' '${folderName}'"
            
            script.sh "rm -rf '${tempDir}'"
            
            if (!script.fileExists(archiveName)) {
                return [success: false, log: "Архив ${archiveName} не был создан"]
            }
            
            return [
                success: true, 
                log: "Архив ${archiveName} успешно создан",
                archiveName: archiveName,
                repoName: repoName,
                version: version
            ]
            
        } catch (Exception e) {
            return [success: false, log: "Ошибка при создании архива: ${e.message}"]
        }
    }

    /**
     * Extracts repository name from package name
     * Removes common prefixes like "com.kefir." if present
     */
    private String extractRepoName(String packageName) {
        if (packageName.startsWith("com.kefir.")) {
            return packageName.substring("com.kefir.".length())
        }
        if (packageName.startsWith("@")) {
            int slashIndex = packageName.indexOf('/')
            if (slashIndex > 0) {
                return packageName.substring(slashIndex + 1)
            }
        }
        return packageName
    }

    /**
     * Copies files to archive directory, excluding specified patterns
     */
    private void copyFilesToArchiveDir(String targetDir, String excludePatterns) {
        String rsyncExcludes = excludePatterns.split(',')
            .collect { pattern -> "--exclude='${pattern.trim()}'" }
            .join(' ')
        
        script.sh """
            rsync -av ${rsyncExcludes} \
                --exclude='${targetDir}' \
                --exclude='temp_archive_*' \
                --exclude='*.zip' \
                ./ '${targetDir}/'
        """
    }

    /**
     * Archives files to a specified output directory
     * @param config Map containing archive configuration and output directory
     * @return Map with success status and log message
     */
    Map createZipArchiveToDirectory(Map config) {
        def result = createZipArchive(config)
        
        if (result.success && config.outputDirectory) {
            try {
                script.sh "mkdir -p '${config.outputDirectory}'"
                script.sh "mv '${result.archiveName}' '${config.outputDirectory}/'"
                
                result.log += " и перемещен в ${config.outputDirectory}/"
                result.archivePath = "${config.outputDirectory}/${result.archiveName}"
                
            } catch (Exception e) {
                return [success: false, log: "Ошибка при перемещении архива: ${e.message}"]
            }
        }
        
        return result
    }
}
