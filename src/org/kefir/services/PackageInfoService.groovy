package org.kefir.services

class PackageInfoService {
    private final def script

    PackageInfoService(script) {
        this.script = script
    }

    /**
     * Extracts package information from package.json in the current workspace
     * @return Map containing package name and version
     */
    Map getPackageInfo() {
        try {
            if (script.fileExists('package.json')) {
                def packageJson = script.readJSON(file: 'package.json')
                return [
                    name: packageJson.name ?: '',
                    version: packageJson.version ?: ''
                ]
            } else {
                script.echo "package.json not found in workspace"
                return [name: '', version: '']
            }
        } catch (Exception e) {
            script.echo "Error reading package.json: ${e.message}"
            return [name: '', version: '']
        }
    }

    /**
     * Validates that package.json contains required fields
     * @return Boolean indicating if package info is valid
     */
    Boolean isPackageInfoValid() {
        def packageInfo = getPackageInfo()
        return packageInfo.name && packageInfo.version
    }

    /**
     * Extracts changelog content for a specific version from CHANGELOG.md
     * @param version The version to extract changelog for (e.g., "1.1.4")
     * @return String containing the changelog content for the version, or empty string if not found
     */
    String getChangelogForVersion(String version) {
        try {
            if (!script.fileExists('CHANGELOG.md')) {
                script.echo "CHANGELOG.md not found in workspace"
                return ''
            }

            String changelogContent = script.readFile('CHANGELOG.md')
            return extractVersionChangelog(changelogContent, version)
        } catch (Exception e) {
            script.echo "Error reading CHANGELOG.md: ${e.message}"
            return ''
        }
    }

    /**
     * Extracts changelog section for a specific version from changelog content
     * @param changelogContent The full changelog content
     * @param version The version to extract (e.g., "1.1.4")
     * @return String containing the version-specific changelog content
     */
    private String extractVersionChangelog(String changelogContent, String version) {
        try {
            String versionHeader = "## ${version}"
            int versionStartIndex = changelogContent.indexOf(versionHeader)

            if (versionStartIndex == -1) {
                script.echo "Version ${version} not found in CHANGELOG.md"
                return ''
            }

            int nextVersionIndex = findNextVersionHeader(changelogContent, versionStartIndex + versionHeader.length())

            String versionChangelog
            if (nextVersionIndex == -1) {
                versionChangelog = changelogContent.substring(versionStartIndex)
            } else {
                versionChangelog = changelogContent.substring(versionStartIndex, nextVersionIndex)
            }

            return versionChangelog.trim()
        } catch (Exception e) {
            script.echo "Error extracting changelog for version ${version}: ${e.message}"
            return ''
        }
    }

    /**
     * Finds the index of the next version header after the current position
     * @param content The changelog content
     * @param startIndex The index to start searching from
     * @return int The index of the next version header, or -1 if not found
     */
    private int findNextVersionHeader(String content, int startIndex) {
        String[] lines = content.substring(startIndex).split('\n')
        int currentIndex = startIndex

        for (String line : lines) {
            if (line.trim().startsWith('## ') && line.trim().length() > 3) {
                String headerContent = line.trim().substring(3).trim()
                if (headerContent.matches('.*\\d+.*')) {
                    return currentIndex
                }
            }
            currentIndex += line.length() + 1
        }

        return -1
    }
}
