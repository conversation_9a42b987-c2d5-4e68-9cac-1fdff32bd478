package org.kefir.configs

class MattermostConstants {
    static final String DEFAULT_CHANNEL = '#builds-devops'
    
    static final String COLOR_GOOD = 'good'
    static final String COLOR_WARNING = 'warning'
    static final String COLOR_DANGER = 'danger'
    
    static final String ICON_PACKAGE = '📦'
    static final String ICON_SUCCESS = '✅'
    static final String ICON_FAILURE = '❌'
    static final String ICON_WARNING = '⚠️'
    static final String ICON_INFO = '📊'

    static final String BUILD_SUCCESS_TEMPLATE = "${ICON_SUCCESS} **Build Success**"
    static final String PACKAGE_PUBLISHED_TEMPLATE = "${ICON_PACKAGE} **Package `%s` version `%s` has been published**"
    static final String BUILD_FAILED_TEMPLATE = "${ICON_FAILURE} **Build Failed**"
    static final String PACKAGE_FAILED_TEMPLATE = "${ICON_FAILURE} **Package `%s` version `%s` failed to be published**"
    static final String BUILD_STATUS_TEMPLATE = "%s **Build Status: %s**"
    
    static final String BUILD_INFO_HEADER = "**Build Information:**"

}
