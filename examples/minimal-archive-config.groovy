// Минимальная конфигурация для включения архивирования
// Этот пример показывает самый простой способ добавить создание ZIP архивов

@Library('jenkins-shared-libraries') _

// Минимальная конфигурация - только включить архивирование
projectPipeline([
    buildConfig: [
        agentLabel: 'gce',
        
        // Публикация npm пакета (обязательно)
        app: [
            enabled: true,
            verdaccioInstance: 'verdaccio',
            nodeJsTool: 'node24.3.0'
        ],
        
        // Архивирование (НОВЫЙ ФУНКЦИОНАЛ) - минимальная настройка
        archive: [
            enabled: true  // Все остальные параметры используют значения по умолчанию
        ],
        
        // Документация и уведомления отключены
        docs: [
            enabled: false
        ],
        
        notifications: [
            enabled: false
        ]
    ]
])

/*
 * Что произойдет с этой конфигурацией:
 * 
 * 1. Будет опубликован npm пакет в Verdaccio
 * 2. Будет создан ZIP архив с настройками по умолчанию:
 *    - Имя архива: {repo-name}-{version}.zip (например, addon-manager-1.1.1.zip)
 *    - Папка в архиве: {repo-name}/ (например, addon-manager/)
 *    - Исключения по умолчанию: node_modules/**, *.log, *.tmp, .git/**, .gitignore, Jenkinsfile*
 *    - Архив сохраняется в корне workspace
 * 
 * Пример структуры созданного архива:
 * addon-manager-1.1.1.zip
 * └── addon-manager/
 *     ├── package.json
 *     ├── src/
 *     │   ├── index.js
 *     │   └── utils/
 *     ├── lib/
 *     ├── README.md
 *     └── ... (другие файлы проекта, кроме исключенных)
 */
