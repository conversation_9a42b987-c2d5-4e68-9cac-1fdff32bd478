// Пример конфигурации buildPipeline с функционалом создания ZIP архивов

@Library('jenkins-shared-libraries') _

// Базовая конфигурация с созданием архива
def config = [
    agentLabel: 'gce',
    
    // NPM package publishing configuration
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio',
        nodeJsTool: 'node24.3.0'
    ],
    
    // ZIP Archive creation configuration
    archive: [
        enabled: true,                                    // Включить создание архива
        excludePatterns: 'node_modules/**,*.log,*.tmp,.git/**,.gitignore,Jenkinsfile*,dist/**', // Исключаемые файлы/папки
        outputDirectory: 'artifacts'                      // Опциональная директория для сохранения архива
    ],
    
    // Documentation publishing (optional)
    docs: [
        enabled: false
    ],
    
    // Mattermost notification configuration
    notifications: [
        enabled: true,
        channel: '#npm-releases'
    ]
]

// Вызов buildPipeline с конфигурацией
buildPipeline(config)

/*
 * Пример результата:
 * 
 * Если package.json содержит:
 * {
 *   "name": "com.kefir.addon-manager",
 *   "version": "1.1.1"
 * }
 * 
 * То будет создан архив: addon-manager-1.1.1.zip
 * Содержащий папку: addon-manager/
 * В которой будут все файлы проекта (кроме исключенных)
 */

// Альтернативная конфигурация с минимальными настройками
def minimalConfig = [
    agentLabel: 'docker',
    
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio',
        nodeJsTool: 'node24.3.0'
    ],
    
    // Минимальная конфигурация архива - использует настройки по умолчанию
    archive: [
        enabled: true
    ],
    
    docs: [
        enabled: false
    ],
    
    notifications: [
        enabled: false
    ]
]

// Раскомментируйте для использования минимальной конфигурации
// buildPipeline(minimalConfig)

// Расширенная конфигурация с кастомными исключениями
def advancedConfig = [
    agentLabel: 'kubernetes',
    
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio-prod',
        nodeJsTool: 'node24.3.0'
    ],
    
    archive: [
        enabled: true,
        // Кастомные паттерны исключения
        excludePatterns: [
            'node_modules/**',
            '*.log',
            '*.tmp',
            '.git/**',
            '.gitignore',
            'Jenkinsfile*',
            'test/**',
            'tests/**',
            '*.test.js',
            '*.spec.js',
            'coverage/**',
            '.nyc_output/**',
            'docs/**',
            '*.md',
            '.vscode/**',
            '.idea/**'
        ].join(','),
        outputDirectory: 'build/artifacts'
    ],
    
    docs: [
        enabled: true,
        confluenceSpaceName: 'DEV',
        confluenceParentPageID: '12345',
        documentationDirectoryPath: 'docs',
        documentationLabel: 'my-package',
        documentationTitle: 'My Package Documentation',
        documentationHomePagePath: 'docs/README.md'
    ],
    
    notifications: [
        enabled: true,
        channel: '#deployment-notifications'
    ]
]

// Раскомментируйте для использования расширенной конфигурации
// buildPipeline(advancedConfig)
