// Полный пример интеграции архивирования с существующим workflow
// Показывает все возможности новой функциональности

@Library('jenkins-shared-libraries') _

/*
 * Этот пример демонстрирует полную интеграцию функционала архивирования
 * с существующим workflow Jenkins Shared Libraries
 */

projectPipeline([
    // Конфигурация валидации (выполняется в Merge Request'ах)
    validateConfig: [
        agentLabel: 'docker',
        checks: [
            packageJson: [
                enabled: true,
                version: true,
                name: true,
                repositoryUrl: true,
                documentationUrl: true,
                changelogUrl: true
            ],
            initPy: [
                enabled: false  // Отключено для npm проектов
            ]
        ]
    ],
    
    // Конфигурация сборки (выполняется в master ветке)
    buildConfig: [
        agentLabel: 'gce',
        
        // 1. Публикация npm пакета в Verdaccio
        app: [
            enabled: true,
            verdaccioInstance: 'verdaccio',
            nodeJsTool: 'node24.3.0'
        ],
        
        // 2. НОВЫЙ ФУНКЦИОНАЛ: Создание ZIP архива
        archive: [
            enabled: true,                                    // Включить архивирование
            excludePatterns: [                               // Кастомные исключения
                'node_modules/**',
                '*.log',
                '*.tmp',
                '.git/**',
                '.gitignore',
                'Jenkinsfile*',
                'test/**',
                'tests/**',
                '*.test.js',
                '*.spec.js',
                'coverage/**',
                '.nyc_output/**',
                'dist/**',
                'build/**',
                '.vscode/**',
                '.idea/**',
                '*.md'
            ].join(','),
            outputDirectory: 'artifacts'                     // Организованное хранение
        ],
        
        // 3. Публикация документации в Confluence
        docs: [
            enabled: true,
            confluenceSpaceName: 'DEV',
            confluenceParentPageID: '12345',
            documentationDirectoryPath: 'docs',
            documentationLabel: 'addon-manager',
            documentationTitle: 'Addon Manager Documentation',
            documentationHomePagePath: 'docs/README.md'
        ],
        
        // 4. Уведомления в Mattermost
        notifications: [
            enabled: true,
            channel: '#npm-releases',
            successChannel: '#npm-releases',
            failureChannel: '#build-failures',
            message: 'Package and archive successfully created!'
        ]
    ]
])

/*
 * WORKFLOW ВЫПОЛНЕНИЯ:
 * 
 * === В Merge Request ===
 * 1. Запускается validatePipeline
 * 2. Проверяется package.json на корректность
 * 3. Результаты валидации добавляются в комментарий MR
 * 
 * === В master ветке ===
 * 1. Запускается buildPipeline со следующими этапами:
 * 
 *    Stage 1: "Публикация приложения"
 *    - Публикует npm пакет в Verdaccio
 *    - Использует конфигурацию из app секции
 * 
 *    Stage 2: "Создание ZIP архива" (НОВЫЙ!)
 *    - Читает package.json для получения имени и версии
 *    - Извлекает имя репозитория (убирает префикс com.kefir.)
 *    - Создает архив: {repo-name}-{version}.zip
 *    - Внутри архива создает папку: {repo-name}/
 *    - Копирует файлы, исключая указанные паттерны
 *    - Сохраняет архив в директории artifacts/
 * 
 *    Stage 3: "Публикация документации в Confluence"
 *    - Публикует документацию из docs/ в Confluence
 * 
 * 2. Post-actions:
 *    - При успехе: отправляет уведомление в #npm-releases
 *    - При ошибке: отправляет уведомление в #build-failures
 * 
 * РЕЗУЛЬТАТ ДЛЯ ПРОЕКТА addon-manager v1.1.1:
 * 
 * Файловая структура после выполнения:
 * workspace/
 * ├── artifacts/
 * │   └── addon-manager-1.1.1.zip    <- Созданный архив
 * ├── package.json
 * ├── src/
 * └── ... (остальные файлы проекта)
 * 
 * Содержимое архива addon-manager-1.1.1.zip:
 * addon-manager/
 * ├── package.json
 * ├── src/
 * │   ├── index.js
 * │   └── components/
 * ├── lib/
 * └── ... (все файлы кроме исключенных)
 * 
 * Verdaccio:
 * - Опубликован пакет com.kefir.addon-manager@1.1.1
 * 
 * Confluence:
 * - Опубликована документация в пространстве DEV
 * 
 * Mattermost:
 * - Отправлено уведомление в #npm-releases о успешной публикации
 */
